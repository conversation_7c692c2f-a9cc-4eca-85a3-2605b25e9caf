/**
 * Upload 组件值转换工具函数
 */

/**
 * 将 Upload 组件的文件对象数组转换为路径字符串数组
 * @param fileList Upload 组件的文件列表
 * @param multiple 是否多选模式
 * @returns 路径字符串或路径字符串数组
 */
export function transformUploadValue(
  fileList: any[],
  multiple: boolean = true,
): null | string | string[] {
  if (!fileList || !Array.isArray(fileList)) {
    return multiple ? [] : null;
  }

  // 转换文件对象数组为路径字符串数组
  const paths = fileList
    .filter((file: any) => file && file.status === 'done')
    .map((file: any) => {
      // 优先使用 response（我们传递的路径），其次使用 url
      if (file.response && typeof file.response === 'string') {
        return file.response;
      } else if (file.url) {
        return file.url;
      }
      return null;
    })
    .filter(Boolean);

  // 如果只有一个文件且不是多选，返回字符串而不是数组
  if (paths.length === 1 && !multiple) {
    return paths[0];
  }

  return paths;
}

/**
 * 将路径字符串转换为 Upload 组件的文件对象
 * @param path 文件路径
 * @param index 文件索引（用于生成 uid）
 * @returns Upload 文件对象
 */
export function pathToFileObject(path: string, index: number = 0): any {
  if (!path) return null;

  return {
    uid: `default-${Date.now()}-${index}`,
    name: path.split('/').pop() || `文件${index + 1}`,
    status: 'done',
    url: path,
    response: path,
  };
}

/**
 * 将路径字符串数组转换为 Upload 组件的文件对象数组
 * @param paths 路径字符串数组
 * @returns Upload 文件对象数组
 */
export function pathsToFileList(paths: string | string[]): any[] {
  if (!paths) return [];

  if (typeof paths === 'string') {
    const fileObj = pathToFileObject(paths);
    return fileObj ? [fileObj] : [];
  }

  if (Array.isArray(paths)) {
    return paths
      .map((path, index) => pathToFileObject(path, index))
      .filter(Boolean);
  }

  return [];
}

/**
 * 检测字段是否是 Upload 类型
 * @param schema 表单 schema
 * @param fieldName 字段名
 * @returns 是否是 Upload 类型
 */
export function isUploadField(schema: any[], fieldName: string): boolean {
  const field = schema.find((item) => item.fieldName === fieldName);
  return field && field.component === 'Upload';
}

/**
 * 获取 Upload 字段的多选配置
 * @param schema 表单 schema
 * @param fieldName 字段名
 * @returns 是否是多选模式
 */
export function getUploadMultiple(schema: any[], fieldName: string): boolean {
  const field = schema.find((item) => item.fieldName === fieldName);
  if (!field || field.component !== 'Upload') {
    return true; // 默认多选
  }

  // 检查 componentProps 中的 multiple 配置
  if (typeof field.componentProps === 'function') {
    // 如果是函数，无法静态获取，返回默认值
    return true;
  }

  return field.componentProps?.multiple !== false;
}

/**
 * 处理表单数据中的 Upload 字段值转换
 * @param formData 表单数据
 * @param schema 表单 schema
 * @returns 处理后的表单数据
 */
export function processUploadFieldsInFormData(
  formData: Record<string, any>,
  schema: any[],
): Record<string, any> {
  const processedData = { ...formData };

  // 遍历所有字段，找出 Upload 类型的字段
  Object.keys(processedData).forEach((fieldName) => {
    if (isUploadField(schema, fieldName)) {
      const multiple = getUploadMultiple(schema, fieldName);
      processedData[fieldName] = transformUploadValue(
        processedData[fieldName],
        multiple,
      );
    }
  });

  return processedData;
}
